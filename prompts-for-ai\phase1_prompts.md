# Phase 1: Cognitive Research Laboratory Foundation - Home Edition Prompts

## Step 1.1: Cognitive Research Orchestrator (CRO) Architecture

```
Create a MATLAB event-driven Cognitive Research Orchestrator (CRO) that serves as the brain of a personal AI trading research assistant. The CRO should:

1. Implement a finite state machine with states: INITIALIZING, RESEARCHING, DISCOVERING, VA<PERSON><PERSON><PERSON><PERSON>, ALERTING, LEARNING, SHUT<PERSON>OWN
2. Use MATLAB's event-driven programming with handle classes for a self-managing research system
3. Create an in-memory message system using containers.Map for inter-module communication
4. Include self-monitoring that tracks:
   - Module health and performance
   - Discovery rate and quality
   - System resource usage
   - Research progress metrics
5. Implement auto-recovery mechanisms:
   - Automatic restart on crashes
   - State persistence and recovery
   - Graceful degradation
   - Windows Task Scheduler integration
6. Create a module registry where components register with:
   - Module name, capabilities, dependencies
   - Research focus areas
   - Performance history
7. Build a Research Journal system that documents all discoveries and insights
8. Implement module lifecycle (initialize, start, pause, resume, stop)
9. Use Parallel Computing Toolbox to run multiple research experiments
10. Include hot-reloading of configuration without restart

Focus: Create a research assistant, not a production system
Required toolboxes: Core MATLAB, Parallel Computing Toolbox (included in Suite for Startups)
Suite Advantage: All 89 toolboxes available for innovative research applications
Output: Complete CRO.m class with ResearchJournal.m and ModuleRegistry.m
```
## Step 1.2: Home Edition Research Environment Setup

```
Create a MATLAB script that sets up a complete Cognitive Trading Research Laboratory for STRATLAB Home Edition. The script should:

1. Verify MATLAB Suite for Startups toolboxes (89 total available):
   CRITICAL: Financial, Econometrics, Statistics & ML, Database, Parallel Computing, Risk Management
   HIGH PRIORITY: Deep Learning, Signal Processing, Optimization, Global Optimization, Reinforcement Learning, System Identification
   INNOVATIVE: Computer Vision, Image Processing, Robotics System, UAV, Sensor Fusion, Communications, Aerospace, Bioinformatics
   - Implement graceful degradation for missing toolboxes
   - Display feature availability based on installed toolboxes
2. Create research laboratory directory structure:
   /STRATLAB_HOME
   ├── /Core (CRO and base classes)
   ├── /Data (local data storage)
   │   ├── /RealTime (live market data)
   │   ├── /Historical (backtesting data)
   │   └── /Features (computed features)
   ├── /StrategyDNA (discovered strategies as .m files)
   │   ├── /Validated (tested strategies)
   │   └── /Experimental (new discoveries)
   ├── /Research
   │   ├── /Discoveries (documented findings)
   │   └── /Reports (performance analysis)
   ├── /Journal (AI's self-documentation)
   ├── /Dashboards (App Designer GUIs)
   ├── /Config (settings and credentials)
   ├── /Logs (system logs)
   └── /Backups (auto-recovery data)
3. Initialize Git repository for version control
4. Set up Windows Task Scheduler scripts for always-on operation
5. Download File Exchange dependencies:
   - WSClient for WebSocket support
   - JSONlab for enhanced JSON parsing
6. Create configuration templates (config.json, credentials_template.json)
7. Generate MATLAB project file (.prj) for environment management
8. Create startup scripts (StartSTRATLAB.m, RunForever.m)
9. Generate README with Home Edition specific instructions
10. Set up example Strategy DNA templates

Output: SetupSTRATLABHome.m that creates complete research environment
Note: No Docker, no deployment tools, focus on research and discovery
Suite Advantage: 89 toolboxes enable unprecedented innovation - use Computer Vision for chart patterns, Robotics for strategy coordination, Aerospace for portfolio dynamics, Bioinformatics for strategy evolution
```
## Step 1.3: Local Data Vault Infrastructure

```
Create a MATLAB local data storage system optimized for trading research. The system should:

1. Implement DataVault class for local-first data management:
   - SQLite database for strategy metadata and performance tracking
   - HDF5 files for efficient time-series data storage
   - MAT files for computed features with compression
   - JSON files for configuration and settings
2. Create data organization structure:
   - Tick data: /Data/RealTime/{symbol}/{date}.h5
   - Bars: /Data/Historical/{timeframe}/{symbol}.h5
   - Features: /Data/Features/{strategy_id}/{feature_name}.mat
   - Strategies: SQLite tables for DNA, parameters, performance
3. Implement efficient data access patterns:
   - Chunked reading for large datasets
   - Memory-mapped files for huge data
   - Parallel data loading capabilities
   - Smart caching of frequently used data
4. Build data quality management:
   - Automatic validation of incoming data
   - Gap detection and handling
   - Outlier identification
   - Data integrity checksums
5. Create Strategy DNA storage:
   - Each strategy saved as executable .m file
   - Metadata in SQLite (creation date, performance, parameters)
   - Version control integration
   - Strategy lineage tracking
6. Implement research data patterns:
   - Feature computation pipeline
   - Result caching system
   - Experiment tracking
   - Performance history
7. Add data lifecycle management:
   - Automatic compression of old data
   - Configurable retention policies
   - Backup scheduling
8. Create data access APIs:
   - Simple methods for common queries
   - Bulk operations for backtesting
   - Real-time streaming interfaces
9. Build performance monitoring for all operations
10. Include example data generators for testing

Required toolboxes: Database Toolbox
Output: Complete DataVault.m with StrategyDNABank.m and example usage
Note: Everything local, no cloud databases needed
```
## Step 1.4: Research Journal & Self-Documentation System

```
Create a MATLAB self-documenting research journal system where the AI assistant records its own discoveries and learning process. The system should:

1. Implement ResearchJournal class that:
   - Creates daily markdown journals with timestamps
   - Categorizes entries (DISCOVERY, INSIGHT, ERROR, LEARNING, PERFORMANCE)
   - Links related discoveries and strategies
   - Generates summary reports
2. Create automatic documentation features:
   - Strategy discovery documentation with full context
   - Performance tracking over time
   - Failed experiment analysis
   - Learning progression tracking
3. Build insight extraction system:
   - Pattern recognition in discoveries
   - Success factor analysis
   - Failure pattern identification
   - Meta-learning observations
4. Implement journal organization:
   - /Journal/Daily/{date}/session_{timestamp}.md
   - /Journal/Insights/{insight_id}.md
   - /Journal/Strategies/{strategy_dna_id}.md
   - /Journal/Weekly/summary_{week}.md
5. Create visualization capabilities:
   - Discovery timeline graphs
   - Performance evolution charts
   - Strategy relationship networks
   - Learning curve analytics
6. Add search and query features:
   - Full-text search across journals
   - Tag-based categorization
   - Time-range queries
   - Performance-based filtering
7. Build export capabilities:
   - PDF report generation
   - HTML dashboard creation
   - CSV data exports
   - Strategy DNA documentation
8. Implement self-reflection features:
   - Daily performance review
   - Weekly pattern analysis
   - Monthly strategy evolution
   - Quarterly meta-analysis
9. Create notification system for significant discoveries
10. Include example journal entries and templates

Required toolboxes: Core MATLAB
Output: ResearchJournal.m with InsightExtractor.m and templates
Note: System documents its own cognitive process for transparency
```
## Step 1.5: Windows Integration & Always-On Operation

```
Create Windows integration scripts and MATLAB code for continuous research operation. The system should:

1. Create Windows Task Scheduler integration:
   - ScheduleSTRATLAB.bat for auto-start configuration
   - RunSTRATLAB.bat for launching MATLAB with recovery
   - Morning startup schedule (pre-market)
   - Crash recovery scheduling
2. Implement RunForever.m continuous operation script:
   - Infinite loop with try-catch protection
   - Automatic crash recovery with state restoration
   - Error logging with full stack traces
   - Configurable restart delays
3. Build system tray integration (using Java):
   - Status indicator (green=running, yellow=processing, red=error)
   - Quick access menu
   - Discovery notifications
   - Performance summary
4. Create health monitoring service:
   - CPU and memory usage tracking
   - Disk space monitoring
   - Network connectivity checks
   - Module health status
5. Implement graceful shutdown procedures:
   - Save all pending discoveries
   - Close database connections
   - Export journal summary
   - Backup current state
6. Add remote monitoring capabilities:
   - Email alerts for discoveries
   - Daily summary emails
   - Error notifications
   - Performance reports
7. Build maintenance routines:
   - Nightly data optimization
   - Weekly backup creation
   - Monthly performance review
   - Quarterly strategy cleanup
8. Create diagnostic tools:
   - System health dashboard
   - Performance profiler
   - Module dependency checker
   - Resource usage analyzer
9. Implement update mechanism:
   - Check for strategy improvements
   - Configuration hot-reload
   - Module updates without restart
10. Include troubleshooting guide and recovery procedures

Required toolboxes: Core MATLAB
Output: Windows integration scripts, RunForever.m, SystemMonitor.m
Note: Designed for 24/7 operation on personal computer
```
## Step 1.6: Research Dashboard Foundation

```
Create the foundation for a beautiful MATLAB App Designer dashboard system for monitoring your AI research assistant. The dashboard should:

1. Design modular dashboard architecture:
   - Main control panel with system status
   - Real-time discovery feed
   - Performance metrics display
   - Strategy DNA browser
   - Research journal viewer
2. Implement dashboard base class:
   - Auto-refresh capabilities
   - Dark/light theme support
   - Responsive layout design
   - Module plugin system
3. Create status monitoring widgets:
   - System health indicators
   - Module status cards
   - Resource usage gauges
   - Discovery rate tracker
4. Build real-time data visualization:
   - Live price charts
   - Strategy performance curves
   - Discovery timeline
   - Learning progression
5. Add interactive controls:
   - Start/stop research modules
   - Adjust research parameters
   - Manual strategy validation
   - Journal entry creation
6. Implement alert system:
   - Visual notifications for discoveries
   - Audio alerts for opportunities
   - Error warning displays
   - Performance milestones
7. Create Strategy DNA viewer:
   - Browse discovered strategies
   - View performance metrics
   - Compare strategies
   - Export strategy code
8. Build research analytics:
   - Discovery success rates
   - Strategy type distribution
   - Performance rankings
   - Time-based analysis
9. Add customization features:
   - Configurable layouts
   - Widget preferences
   - Alert settings
   - Display options
10. Include demo mode with simulated discoveries

Required toolboxes: Core MATLAB (App Designer included)
Output: DashboardBase.m, MainDashboard.mlapp, example widgets
Note: Local GUI, no web server needed
```

## Summary of Phase 1 Home Edition Changes

All prompts have been updated to reflect:
- **No Docker**: Removed all containerization references
- **No Deployment Tools**: No MATLAB Compiler or Production Server
- **Research Focus**: Emphasis on discovery and learning
- **Local-First**: All data and processing stays local
- **Strategy DNA**: Strategies saved as reusable .m files
- **Self-Documentation**: AI journals its own discoveries
- **Windows Integration**: Task Scheduler for always-on operation
- **App Designer**: Local GUIs instead of web interfaces

The system is now designed as a "Cognitive Trading Research Laboratory" that helps you discover and validate trading strategies while staying within Home Edition license limits.