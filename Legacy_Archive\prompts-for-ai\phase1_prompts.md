# Phase 1: Cognitive Research Laboratory Foundation - Home Edition Prompts (ARCHIVED)

**MIGRATION NOTE:** This file has been archived as part of the Cross Domain Capital optimization.
**SUPERSEDED BY:** 01_Foundation_Session.md with improved AI prompts and structure
**REASON:** Original prompt system fragmented; replaced by comprehensive session guides
**SEE:** Legacy_Archive/Migration_History.md for complete migration details.

---

# ORIGINAL CONTENT BELOW

# Phase 1: Cognitive Research Laboratory Foundation - Home Edition Prompts

## Step 1.1: Cognitive Research Orchestrator (CRO) Architecture

```
Create a MATLAB event-driven Cognitive Research Orchestrator (CRO) that serves as the brain of a personal AI trading research assistant. The CRO should:

1. Implement a finite state machine with states: INITIALIZING, RESEARCHING, D<PERSON><PERSON><PERSON>RING, <PERSON><PERSON><PERSON><PERSON>ING, ALERTING, LEARNING, SHUTDOWN
2. Use MATL<PERSON>'s event-driven programming with handle classes for a self-managing research system
3. Create an in-memory message system using containers.Map for inter-module communication
4. Include self-monitoring that tracks:
   - Module health and performance
   - Discovery rate and quality
   - System resource usage
   - Research progress metrics
5. Implement auto-recovery mechanisms:
   - Automatic restart on crashes
   - State persistence and recovery
   - Graceful degradation
   - Windows Task Scheduler integration
6. Create a module registry where components register with:
   - Module name, capabilities, dependencies
   - Research focus areas
   - Performance history
7. Build a Research Journal system that documents all discoveries and insights
8. Implement module lifecycle (initialize, start, pause, resume, stop)
9. Use Parallel Computing Toolbox to run multiple research experiments
10. Include hot-reloading of configuration without restart

Focus: Create a research assistant, not a production system
Required toolboxes: Core MATLAB, Parallel Computing Toolbox (included in Suite for Startups)
Suite Advantage: All 89 toolboxes available for innovative research applications
Output: Complete CRO.m class with ResearchJournal.m and ModuleRegistry.m
```

## Step 1.2: Home Edition Research Environment Setup

```
Create a MATLAB script that sets up a complete Cognitive Trading Research Laboratory for STRATLAB Home Edition. The script should:

1. Verify MATLAB Suite for Startups toolboxes (89 total available):
   CRITICAL: Financial, Econometrics, Statistics & ML, Database, Parallel Computing, Risk Management
   HIGH PRIORITY: Deep Learning, Signal Processing, Optimization, Global Optimization, Reinforcement Learning, System Identification
   INNOVATIVE: Computer Vision, Image Processing, Robotics System, UAV, Sensor Fusion, Communications, Aerospace, Bioinformatics
   - Implement graceful degradation for missing toolboxes
   - Display feature availability based on installed toolboxes
2. Create research laboratory directory structure:
   /STRATLAB_HOME
   ├── /Core (CRO and base classes)
   ├── /Data (local data storage)
   │   ├── /RealTime (live market data)
   │   ├── /Historical (historical data)
   │   └── /Features (computed features)
   ├── /Strategies (strategy implementations)
   ├── /StrategyDNA (discovered strategies)
   ├── /Research (research outputs)
   ├── /Journal (AI self-documentation)
   ├── /Dashboards (App Designer GUIs)
   ├── /Config (configuration files)
   ├── /Logs (system logs)
   └── /Backups (auto-recovery data)
3. Initialize local databases:
   - SQLite for strategy metadata and performance
   - HDF5 for high-frequency time series data
   - MAT files for computed features and models
4. Create configuration templates:
   - config.json with all system parameters
   - credentials_template.json for API keys
   - risk_limits.json for safety parameters
5. Set up Windows Task Scheduler integration for auto-start
6. Create desktop shortcuts and MATLAB startup integration
7. Initialize the CRO and verify all modules load correctly

Output: SetupSTRATLABHome.m that creates complete research environment
Note: No Docker, no deployment tools, focus on research and discovery
Suite Advantage: 89 toolboxes enable unprecedented innovation - use Computer Vision for chart patterns, Robotics for strategy coordination, Aerospace for portfolio dynamics, Bioinformatics for strategy evolution
```

## Step 1.3: Local Data Vault Architecture

```
Create a MATLAB DataVault class for STRATLAB Home Edition that provides local-first data management. The class should:

1. Implement multi-format data storage:
   - SQLite for strategy metadata, performance tracking, and research notes
   - HDF5 for high-frequency time series data (tick data, 1-minute bars)
   - MAT files for computed features, models, and cached results
   - JSON for configuration and lightweight data exchange
2. Create data quality firewall:
   - Real-time validation of incoming data
   - Missing data detection and interpolation
   - Outlier detection and flagging
   - Cross-source consistency checks
   - Data lineage tracking
3. Implement intelligent caching:
   - In-memory cache for recent data (last 1000 bars per instrument)
   - Automatic cache warming on startup
   - Cache invalidation and refresh strategies
   - Memory usage monitoring and cleanup
4. Build data versioning system:
   - Version control for datasets
   - Rollback capabilities
   - Change tracking and audit logs
   - Backup and recovery procedures
5. Create data access APIs:
   - Unified interface for all data types
   - Query optimization and indexing
   - Batch and streaming data access
   - Data export capabilities
6. Include data synchronization:
   - MATLAB Drive integration for cloud backup
   - Multi-device synchronization
   - Conflict resolution strategies
   - Offline operation support

Focus: Local-first architecture that works without internet
Required toolboxes: Database Toolbox, Parallel Computing Toolbox
Output: Complete DataVault.m class with supporting utilities
```

## Step 1.4: Research Journal and Self-Documentation

```
Create a MATLAB ResearchJournal class that enables the AI system to document its own discoveries and learning process. The class should:

1. Implement automated discovery logging:
   - Strategy discovery events with confidence scores
   - Market insight generation and validation
   - Performance milestone tracking
   - Research breakthrough identification
2. Create structured knowledge base:
   - Searchable discovery database
   - Categorized insights and patterns
   - Cross-referenced research topics
   - Hypothesis tracking and validation
3. Build narrative generation:
   - Natural language summaries of discoveries
   - Research progress reports
   - Strategy evolution documentation
   - Market condition analysis
4. Include visualization capabilities:
   - Discovery timeline charts
   - Performance attribution graphs
   - Research topic networks
   - Strategy genealogy trees
5. Create export and sharing:
   - PDF report generation
   - HTML dashboard creation
   - JSON data export
   - Research collaboration features
6. Implement learning analytics:
   - Discovery rate tracking
   - Research efficiency metrics
   - Knowledge accumulation analysis
   - Insight quality assessment

Focus: AI system that documents its own learning and discoveries
Required toolboxes: Text Analytics Toolbox (if available), Statistics & ML Toolbox
Output: Complete ResearchJournal.m class with reporting utilities
```

## Step 1.5: Module Registry and Lifecycle Management

```
Create a MATLAB ModuleRegistry class that manages all system components and their interactions. The class should:

1. Implement module registration system:
   - Dynamic module discovery and loading
   - Capability declaration and matching
   - Dependency resolution and validation
   - Version compatibility checking
2. Create lifecycle management:
   - Module initialization sequences
   - Start/stop/pause/resume operations
   - Health monitoring and status tracking
   - Automatic restart on failures
3. Build inter-module communication:
   - Message passing system
   - Event broadcasting and subscription
   - Data sharing mechanisms
   - Service discovery and binding
4. Include performance monitoring:
   - Resource usage tracking
   - Performance metrics collection
   - Bottleneck identification
   - Optimization recommendations
5. Create configuration management:
   - Module-specific configuration
   - Dynamic reconfiguration
   - Configuration validation
   - Settings persistence
6. Implement hot-swapping:
   - Runtime module replacement
   - Graceful shutdown procedures
   - State preservation and restoration
   - Rollback capabilities

Focus: Robust module management for a self-managing research system
Required toolboxes: Core MATLAB, Parallel Computing Toolbox
Output: Complete ModuleRegistry.m class with module templates
```

## Step 1.6: Parallel Research Execution Framework

```
Create a MATLAB ParallelResearchFramework that enables concurrent strategy discovery and validation. The framework should:

1. Implement parallel strategy discovery:
   - Multiple discovery algorithms running simultaneously
   - Resource allocation and load balancing
   - Result aggregation and ranking
   - Conflict resolution between discoveries
2. Create distributed backtesting:
   - Parallel validation of multiple strategies
   - Time period partitioning
   - Cross-validation coordination
   - Performance aggregation
3. Build experiment management:
   - Experiment queue and scheduling
   - Resource reservation system
   - Progress tracking and reporting
   - Result caching and reuse
4. Include fault tolerance:
   - Worker failure detection and recovery
   - Checkpoint and resume capabilities
   - Result validation and verification
   - Automatic retry mechanisms
5. Create monitoring and control:
   - Real-time progress visualization
   - Resource usage monitoring
   - Performance optimization
   - Manual intervention capabilities
6. Implement result management:
   - Structured result storage
   - Performance comparison tools
   - Statistical significance testing
   - Report generation

Focus: Maximize research throughput using parallel computing
Required toolboxes: Parallel Computing Toolbox
Output: Complete ParallelResearchFramework.m class with worker templates
```

## Integration Requirements

All Phase 1 components must integrate seamlessly:

1. **CRO** orchestrates all other components
2. **DataVault** provides data services to all modules
3. **ResearchJournal** logs activities from all components
4. **ModuleRegistry** manages all system modules
5. **ParallelResearchFramework** executes research tasks

## Success Criteria

Phase 1 is complete when:
- [ ] CRO can start, manage, and stop all system components
- [ ] DataVault can store and retrieve all data types
- [ ] ResearchJournal documents system activities
- [ ] ModuleRegistry manages module lifecycle
- [ ] ParallelResearchFramework executes concurrent tasks
- [ ] All components integrate without conflicts
- [ ] System can recover from failures automatically
- [ ] Setup script creates working environment in under 5 minutes

## Next Phase Preparation

Phase 1 creates the foundation for:
- **Phase 2**: Multi-source data integration
- **Phase 3**: AI strategy discovery engines
- **Phase 4**: Advanced validation frameworks
- **Phase 5**: User interface and monitoring
- **Phase 6**: Production deployment and monitoring

The cognitive research laboratory is now ready to begin discovering trading strategies!
