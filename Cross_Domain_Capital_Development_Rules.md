# Cross Domain Capital: Development Rules & Standards

## 🚨 CRITICAL PROJECT CONTEXT

**Project:** Cross Domain Capital (formerly STRATLAB)  
**Type:** Self-evolving algorithmic trading system  
**Platform:** MATLAB Suite for Startups (89 toolboxes)  
**Innovation:** Cross-domain engineering applied to finance  
**Development:** AI-assisted, systematic, 7-session implementation  

## 🔒 ABSOLUTE LANGUAGE RESTRICTION

### **MATLAB ONLY - NO EXCEPTIONS**
- ✅ **ALLOWED:** MATLAB code (.m files, .mlx files)
- ❌ **FORBIDDEN:** Python, R, C++, Java, JavaScript, or ANY other language
- ❌ **FORBIDDEN:** External language integrations or wrappers
- ❌ **FORBIDDEN:** Suggesting alternative platforms or languages

**RATIONALE:** Cross Domain Capital is specifically designed for MATLAB Suite for Startups to leverage all 89 toolboxes for cross-domain innovation. Any suggestion of other languages violates the core project architecture.

## 📋 MANDATORY SESSION PROTOCOL

### **Every AI Development Session MUST:**
1. **FIRST ACTION:** Load and reference `AI_Context_Manager.md`
2. **SECOND ACTION:** Check `Module_Dependencies.md` for build order
3. **THIRD ACTION:** Load appropriate session guide (01-07)
4. **DURING DEVELOPMENT:** Follow Cross Domain Capital architecture
5. **FINAL ACTION:** Update Module Registry with completed components

### **Session Sequence (MUST FOLLOW IN ORDER):**
```
01_Foundation_Session.md → 02_Data_Integration_Session.md → 
03_Strategy_Discovery_Session.md → 04_Validation_Framework_Session.md → 
05_UI_Dashboard_Session.md → 06_Integration_Testing_Session.md → 
07_Deployment_Session.md
```

## 🏗️ MATLAB ARCHITECTURE STANDARDS

### **Class Structure Requirements:**
```matlab
classdef MyClass < handle  % MUST inherit from handle for system components
    properties (Access = private)
        % Use camelCase for properties
        dataManager
        strategyEngine
    end
    
    properties (Access = public)
        % Public properties for configuration
        isActive = false
        lastUpdate
    end
    
    methods (Access = public)
        function obj = MyClass()
            % Constructor with comprehensive initialization
            obj.initialize();
        end
        
        function result = myMethod(obj, param1, param2)
            % Use camelCase for methods
            % Include comprehensive error handling
            try
                result = obj.processData(param1, param2);
            catch ME
                obj.logError(ME);
                rethrow(ME);
            end
        end
    end
    
    methods (Access = private)
        function initialize(obj)
            % Private initialization method
        end
    end
end
```

### **Naming Conventions (MANDATORY):**
- **Classes:** PascalCase (e.g., `CognitiveResearchOrchestrator`)
- **Methods:** camelCase (e.g., `discoverStrategies`)
- **Properties:** camelCase (e.g., `strategyDatabase`)
- **Constants:** UPPER_CASE (e.g., `MAX_STRATEGIES`)
- **Files:** PascalCase matching class name (e.g., `DataVault.m`)

## 🧬 CROSS-DOMAIN INNOVATION REQUIREMENTS

### **Mandatory Cross-Domain Documentation:**
```matlab
classdef GeneticStrategyEvolver < handle
    % GeneticStrategyEvolver - Evolves trading strategies using genetic algorithms
    %
    % CROSS-DOMAIN INSPIRATION: Bioinformatics genetic evolution
    % - Strategy DNA encoding like genetic sequences
    % - Population evolution with selection pressure
    % - Phylogenetic trees for strategy relationships
    %
    % COMPETITIVE ADVANTAGE: Traditional finance cannot access bioinformatics
    % toolbox capabilities for strategy evolution
    %
    % REQUIRED TOOLBOXES: Bioinformatics Toolbox, Global Optimization Toolbox
```

### **Cross-Domain Applications (MUST IMPLEMENT):**
- **Aerospace:** Orbital mechanics for portfolio optimization
- **Robotics:** Multi-agent coordination for strategy management
- **Computer Vision:** Chart pattern recognition and visual analysis
- **Bioinformatics:** Genetic algorithm strategy evolution
- **Communications:** Signal processing for market microstructure
- **Control Systems:** Feedback loops for risk management

## 🖥️ PROGRAMMATIC UI STANDARDS

### **UI Creation Pattern (MANDATORY):**
```matlab
function myDashboard()
    % Create main figure
    fig = uifigure('Name', 'Cross Domain Capital - Dashboard', ...
                   'Position', [100, 100, 1200, 800]);
    
    % Create grid layout
    gl = uigridlayout(fig, [4, 3]);
    gl.RowHeight = {'fit', '1x', '1x', 'fit'};
    gl.ColumnWidth = {'1x', '2x', '1x'};
    
    % Add components with explicit positioning
    btn = uibutton(gl, 'Text', 'Discover Strategies');
    btn.Layout.Row = 1;
    btn.Layout.Column = 1;
    btn.ButtonPushedFcn = @(src,event) discoverStrategies();
    
    % Add real-time update timer
    timer = timer('ExecutionMode', 'fixedRate', ...
                  'Period', 1, ...
                  'TimerFcn', @(src,event) updateDisplay());
    start(timer);
end
```

### **UI Requirements:**
- ✅ **USE:** uifigure, uigridlayout, uipanel, uibutton, uiaxes, uitable
- ❌ **AVOID:** App Designer (.mlapp files) - use programmatic creation only
- **LAYOUT:** Always use grid layouts for responsive design
- **CALLBACKS:** Use anonymous functions or nested functions
- **UPDATES:** Implement timer-based real-time updates

## 🔧 MATLAB SUITE FOR STARTUPS UTILIZATION

### **Toolbox Usage Requirements:**
```matlab
% ALWAYS check toolbox availability
if license('test', 'Computer_Vision_Toolbox')
    % Use computer vision features
    result = detectChartPatterns(priceData);
else
    % Graceful degradation
    warning('Computer Vision Toolbox not available - using basic pattern detection');
    result = basicPatternDetection(priceData);
end
```

### **Priority Toolbox Usage:**
- **CRITICAL:** Financial, Econometrics, Statistics & ML, Database, Parallel Computing, Risk Management
- **HIGH:** Deep Learning, Signal Processing, Optimization, Global Optimization, Reinforcement Learning
- **INNOVATIVE:** Computer Vision, Bioinformatics, Aerospace, Robotics System, Communications

## 🏛️ SYSTEM ARCHITECTURE COMPLIANCE

### **Integration Requirements (MANDATORY):**
```matlab
classdef MyModule < handle
    properties (Access = private)
        orchestrator  % Reference to CognitiveResearchOrchestrator
        dataVault     % Reference to DataVault
        journal       % Reference to ResearchJournal
    end
    
    methods (Access = public)
        function obj = MyModule(orchestrator)
            % MUST integrate with CognitiveResearchOrchestrator
            obj.orchestrator = orchestrator;
            obj.dataVault = orchestrator.getDataVault();
            obj.journal = orchestrator.getResearchJournal();
            
            % Register with ModuleRegistry
            orchestrator.registerModule(obj);
        end
    end
end
```

### **Module Integration Rules:**
- **ALL modules MUST:** Inherit from handle class
- **ALL modules MUST:** Register with CognitiveResearchOrchestrator
- **ALL data access MUST:** Go through DataVault
- **ALL logging MUST:** Use ResearchJournal
- **ALL errors MUST:** Be handled and logged

## 📊 CODE QUALITY STANDARDS

### **Error Handling (MANDATORY):**
```matlab
function result = processData(obj, data)
    try
        % Validate inputs
        validateattributes(data, {'double'}, {'nonempty', 'finite'});
        
        % Process data
        result = obj.performCalculation(data);
        
        % Log success
        obj.journal.logInfo('Data processing completed successfully');
        
    catch ME
        % Log error with context
        obj.journal.logError(sprintf('Data processing failed: %s', ME.message));
        
        % Re-throw with additional context
        newME = MException('CrossDomainCapital:ProcessingError', ...
                          'Failed to process data in %s', class(obj));
        newME = addCause(newME, ME);
        throw(newME);
    end
end
```

### **Performance Requirements:**
- **Real-time data:** < 100ms latency
- **Strategy discovery:** Efficient parallel processing
- **Memory management:** Proper cleanup and garbage collection
- **Database queries:** Optimized for high-frequency access

## 📝 DOCUMENTATION STANDARDS

### **Class Documentation (MANDATORY):**
```matlab
classdef MyClass < handle
    % MyClass - Brief description of class purpose
    %
    % CROSS-DOMAIN INSPIRATION: [Specific engineering domain and application]
    % COMPETITIVE ADVANTAGE: [How this provides unique trading advantage]
    % REQUIRED TOOLBOXES: [List of MATLAB toolboxes used]
    %
    % Usage:
    %   obj = MyClass();
    %   result = obj.performOperation(data);
    %
    % See also: RelatedClass1, RelatedClass2
```

### **Method Documentation:**
```matlab
function result = myMethod(obj, param1, param2)
    % myMethod - Brief description of method purpose
    %
    % Inputs:
    %   param1 - Description and type
    %   param2 - Description and type
    %
    % Outputs:
    %   result - Description and type
    %
    % Cross-Domain Note: [How this method applies cross-domain innovation]
```

## 🚨 FORBIDDEN PRACTICES

### **NEVER DO:**
- ❌ Suggest Python, R, or other languages
- ❌ Create .mlapp files (use programmatic UI only)
- ❌ Skip dependency checking in Module_Dependencies.md
- ❌ Create modules without CognitiveResearchOrchestrator integration
- ❌ Use external databases without DataVault abstraction
- ❌ Implement logging without ResearchJournal
- ❌ Skip cross-domain inspiration documentation

### **ALWAYS DO:**
- ✅ Load AI_Context_Manager.md at session start
- ✅ Follow 7-session development sequence
- ✅ Use MATLAB Suite for Startups toolboxes
- ✅ Implement comprehensive error handling
- ✅ Document cross-domain inspirations
- ✅ Update Module Registry after completion

## 🎯 SUCCESS VALIDATION

### **Code Review Checklist:**
- [ ] MATLAB-only implementation
- [ ] Proper naming conventions used
- [ ] Cross-domain inspiration documented
- [ ] Integration with system architecture
- [ ] Comprehensive error handling
- [ ] Performance optimization implemented
- [ ] Programmatic UI creation (if applicable)
- [ ] Module Registry updated

---

## 📢 ENFORCEMENT NOTICE

**These rules are MANDATORY for all Cross Domain Capital development. Any deviation from these standards, especially language restrictions or architectural requirements, is a violation of the project specifications.**

**When in doubt, reference this file and the AI_Context_Manager.md for complete project context.**

**Remember: Cross Domain Capital's competitive advantage comes from innovative MATLAB applications that traditional finance cannot achieve.**
