# Prompts for AI - ARCHIVED

**MIGRATION NOTE:** This entire folder has been archived as part of the Cross Domain Capital optimization.

**SUPERSEDED BY:** Individual session guides (01_Foundation_Session.md through 07_Deployment_Session.md)

**REASON:** Original prompt system was fragmented and difficult for AI to navigate. New session guides provide comprehensive, self-contained development instructions with better context management.

**SEE:** Legacy_Archive/Migration_History.md for complete migration details.

---

## ORIGINAL PROMPT FILES

The following files were part of the original 9-phase prompt system:

- `phase1_prompts.md` → Superseded by `01_Foundation_Session.md`
- `phase2_prompts.md` → Superseded by `02_Data_Integration_Session.md`
- `phase2_prompts_home.md` → Superseded by `02_Data_Integration_Session.md`
- `phase3_prompts.md` → Superseded by `03_Strategy_Discovery_Session.md`
- `phase4_prompts.md` → Superseded by `04_Validation_Framework_Session.md`
- `phase5_prompts.md` → Superseded by `05_UI_Dashboard_Session.md`
- `phase6_prompts.md` → Superseded by `06_Integration_Testing_Session.md`
- `phase7_prompts.md` → Superseded by `07_Deployment_Session.md`
- `phase8_prompts.md` → Content integrated into relevant session guides
- `phase9_prompts.md` → Content integrated into relevant session guides

## NEW SYSTEM ADVANTAGES

The new 7-session system provides:

1. **Better AI Context Management** - Each session is self-contained
2. **Clearer Dependencies** - Explicit build order and prerequisites
3. **Comprehensive Instructions** - Everything needed for each phase in one place
4. **Cross-Domain Innovation Focus** - Systematic innovation tracking
5. **Integration Testing** - Built-in validation at each step

## ACCESSING NEW SYSTEM

To use the new AI-optimized development system:

1. Load `AI_Context_Manager.md` for complete project context
2. Review `Module_Dependencies.md` for build order
3. Follow session guides 01-07 in sequence
4. Update Module Registry after each completed component

The new system is designed for systematic, AI-assisted development with context preservation across multiple coding sessions.
