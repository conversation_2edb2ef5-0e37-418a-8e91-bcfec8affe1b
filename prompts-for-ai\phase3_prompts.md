# Phase 3: Poly-Model Strategy Discovery Engine - Code Generation Prompts

## Step 3.1: Strategy Discovery Framework

```
Create a MATLAB comprehensive strategy discovery system that implements multiple paradigms. The system should:

1. Design unified strategy interface and base classes:
   - IStrategy interface with standard methods (initialize, generateSignals, updateState)
   - Abstract base class with common functionality
   - Strategy metadata (name, version, parameters, requirements)
   - Standardized signal output format (-1 to +1 continuous)
   - Position sizing hints and confidence scores
2. Implement supervised learning strategies:
   - XGBoost wrapper using MATLAB-Python interface
     * Feature importance extraction
     * Hyperparameter optimization with Bayesian search
     * Cross-validation with time series awareness
   - Random Forest implementation
     * Native MATLAB TreeBagger
     * Out-of-bag error tracking
     * Variable importance plots
   - Neural Network strategies
     * Feedforward networks with dropout
     * Automatic architecture search
     * Regularization techniques (L1, L2, dropout)
3. Create deep learning strategy templates:
   - LSTM networks for sequence modeling
     * Multi-layer LSTM with attention
     * Stateful vs stateless variants
     * Sequence length optimization
     * Handle variable length inputs
   - Transformer architectures
     * Self-attention mechanisms
     * Positional encoding for time series
     * Multi-head attention implementation
   - Graph Neural Networks for market relationships
     * Dynamic graph construction from correlations
     * Message passing between assets
     * Temporal graph evolution
4. Build genetic programming system:
   - Tree-based GP for rule evolution
     * Function set: technical indicators, math operations, logical operators
     * Terminal set: price, volume, features
     * Crossover and mutation operators
     * Bloat control mechanisms
   - Grammar-based GP
     * Define trading rule grammar
     * Ensure syntactically correct rules
     * Domain-specific constraints
   - Multi-objective optimization
     * Pareto frontier tracking
     * Balance return vs risk vs complexity
5. Implement statistical arbitrage strategies:
   - Cointegration-based pairs trading
     * Johansen test implementation
     * Dynamic hedge ratio calculation
     * Half-life estimation
     * Spread z-score monitoring
   - Mean reversion strategies
     * Ornstein-Uhlenbeck process fitting
     * Multiple timeframe analysis
     * Regime-dependent parameters
   - Momentum strategies
     * Time series momentum
     * Cross-sectional momentum
     * Momentum crash protection
6. Create reinforcement learning strategies:
   - Deep Q-Learning (DQN)
     * Experience replay buffer
     * Target network updates
     * Epsilon-greedy exploration
   - Policy Gradient methods
     * REINFORCE with baseline
     * Actor-Critic architecture
     * Proximal Policy Optimization (PPO)
   - Multi-agent RL for portfolio
     * Competitive and cooperative agents
     * Communication between agents
7. Build strategy template library:
   - Breakout strategies (Bollinger, Donchian, ATR-based)
   - Market making templates (spread capture, inventory management)
   - Event-driven templates (news, economic releases)
   - Seasonality exploiters (day-of-week, monthly, crop cycles)
   - Microstructure strategies (order flow, book imbalance)
8. Implement hypothesis testing framework:
   - Hypothesis generator from market observations
   - Statistical validation pipeline
   - A/B testing framework for strategies
   - Hypothesis combination logic
   - Failure analysis and learning
9. Create automated parameter optimization:
   - Grid search with early stopping
   - Random search with budget allocation
   - Bayesian optimization with GP surrogate
   - Genetic algorithms for discrete parameters
   - Walk-forward parameter selection
10. Build strategy lifecycle management:
    - Strategy registration and versioning
    - Performance tracking database
    - Automatic deactivation triggers
    - Strategy correlation monitoring
    - Resource allocation optimization

Required toolboxes: Deep Learning, Statistics & ML, Reinforcement Learning, Global Optimization (all included in Suite for Startups)
INNOVATIVE APPLICATIONS: Use Bioinformatics Toolbox for genetic algorithm strategy evolution, Computer Vision for chart pattern strategies, Robotics System Toolbox for multi-agent strategy coordination
Suite Advantage: 89 toolboxes enable unprecedented strategy innovation beyond traditional finance
Output: Complete StrategyDiscovery package with 20+ ready-to-use strategy templates + innovative cross-domain strategies
```

## Step 3.2: Market Regime Detection System

```
Create a MATLAB hybrid market regime detection system. The system should:

1. Implement Markov-switching models:
   - Hidden Markov Model (HMM) base framework
     * Gaussian emissions for returns
     * Student-t emissions for heavy tails
     * Multivariate emissions for multiple assets
   - Regime-switching GARCH
     * Different volatility dynamics per regime
     * Transition probability modeling
     * Likelihood ratio tests
   - Economic indicator integration
     * Include macro variables in transition matrix
     * Time-varying transition probabilities
     * Exogenous variable handling
2. Create fuzzy logic classification system:
   - Define market state membership functions
     * Trending (strong up, up, sideways, down, strong down)
     * Volatility (very low, low, normal, high, extreme)
     * Liquidity (abundant, normal, scarce, dried up)
   - Build fuzzy inference engine
     * Mamdani-type inference
     * Defuzzification methods
     * Rule base optimization
   - Design adaptive membership functions
     * Learn from historical regimes
     * Online adaptation to new patterns
3. Develop clustering-based regime detection:
   - K-means clustering on feature space
     * Optimal K selection using elbow method
     * Silhouette analysis
     * Stability testing across time
   - DBSCAN for anomaly detection
     * Identify unusual market conditions
     * No predefined number of clusters
     * Noise point handling
   - Gaussian Mixture Models
     * Soft cluster assignments
     * Probabilistic regime membership
     * BIC/AIC for model selection
4. Build regime-specific feature engineering:
   - Volatility regimes
     * Realized volatility clustering
     * GARCH regime identification
     * Jump detection algorithms
   - Trend regimes
     * Momentum strength indicators
     * Trend quality metrics
     * Reversal probability estimation
   - Correlation regimes
     * Dynamic correlation matrices
     * Network topology changes
     * Systemic risk indicators
5. Implement CFTC positioning regime analysis:
   - Commercial vs speculator positioning
     * Extreme positioning detection
     * Position momentum indicators
     * Crowding metrics
   - Open interest regimes
     * Expansion/contraction phases
     * Relationship with volatility
   - Cross-market positioning
     * Risk-on/risk-off detection
     * Flight-to-quality indicators
6. Create regime transition detection:
   - Change point detection algorithms
     * CUSUM statistics
     * Bayesian change point
     * Multiple change point detection
   - Early warning indicators
     * Regime instability metrics
     * Transition probability spikes
     * Cross-asset confirmation
   - Regime duration modeling
     * Survival analysis
     * Hazard rate estimation
7. Build adaptive regime forecasting:
   - Regime probability forecasting
     * One-step ahead predictions
     * Multi-step Monte Carlo
     * Confidence intervals
   - Feature importance per regime
     * Regime-specific predictors
     * Dynamic feature selection
   - Ensemble regime predictions
     * Combine multiple models
     * Weighted by recent accuracy
8. Implement regime-aware backtesting:
   - Separate performance by regime
   - Regime-specific risk limits
   - Strategy switching logic
   - Transition period handling
   - Out-of-sample regime detection
9. Create regime visualization tools:
   - Regime timeline plots
   - Transition probability heatmaps
   - 3D regime space visualization
   - Real-time regime dashboard
   - Historical regime analysis
10. Build regime persistence analysis:
    - Average regime duration
    - Regime recurrence patterns
    - Seasonal regime effects
    - Long-term regime shifts
    - Regime stability metrics

Required toolboxes: Fuzzy Logic, Econometrics, Statistics & ML Toolboxes
Output: Complete RegimeDetection package with real-time regime identification
```

## Step 3.3: Causal Inference Engine

```
Create a MATLAB causal discovery and inference system for financial markets. The system should:

1. Implement Granger causality framework:
   - Bivariate Granger causality tests
     * Optimal lag selection (AIC/BIC)
     * F-statistics and p-values
     * Effect size measurement
   - Multivariate Granger causality
     * Conditional causality testing
     * Block exogeneity tests
     * Toda-Yamamoto procedure
   - Frequency domain causality
     * Spectral Granger causality
     * Phase relationships
     * Lead-lag at different frequencies
2. Build structural equation modeling (SEM):
   - Path analysis implementation
     * Direct and indirect effects
     * Mediation analysis
     * Model fit statistics
   - Latent variable models
     * Factor extraction
     * Measurement models
     * Structural models
   - Dynamic factor models
     * Time-varying loadings
     * State-space representation
3. Create directed acyclic graph (DAG) learning:
   - PC algorithm implementation
     * Constraint-based learning
     * Conditional independence tests
     * Skeleton and orientation
   - GES (Greedy Equivalence Search)
     * Score-based approach
     * BIC scoring function
     * Edge addition/deletion
   - Hybrid algorithms
     * Combine constraint and score
     * Bootstrap stability
     * Consensus DAGs
4. Implement causal feature discovery:
   - Markov blanket identification
     * Parents, children, spouses
     * Feature reduction
     * Causal feature selection
   - Instrumental variable detection
     * Automated IV search
     * Weak instrument tests
     * Over-identification tests
   - Confounding detection
     * Hidden variable indicators
     * Deconfounding strategies
5. Build economic causality mapping:
   - Macro to market causality
     * Interest rates → asset prices
     * Economic indicators → sectors
     * Policy → market dynamics
   - Cross-market spillovers
     * Volatility transmission
     * Return spillovers
     * Liquidity contagion
   - Lead-lag relationships
     * Information flow mapping
     * Price discovery analysis
6. Create intervention analysis framework:
   - Do-calculus implementation
     * Causal effect estimation
     * Backdoor adjustment
     * Front-door adjustment
   - Synthetic control methods
     * Counterfactual construction
     * Treatment effect estimation
     * Placebo tests
   - Natural experiments
     * Event identification
     * Difference-in-differences
     * Regression discontinuity
7. Implement time-varying causality:
   - Rolling window causality
     * Dynamic relationships
     * Stability testing
     * Break point detection
   - State-dependent causality
     * Regime-specific relationships
     * Threshold effects
     * Smooth transitions
   - Evolutionary causality
     * Gradual relationship changes
     * Structural breaks
     * Long-term trends
8. Build causality-based prediction:
   - Causal feature engineering
     * Use only causal parents
     * Avoid spurious correlations
     * Robust predictions
   - Causal ensemble methods
     * Combine causal models
     * Uncertainty quantification
     * Causal regularization
   - Transfer learning
     * Apply causality across markets
     * Domain adaptation
9. Create causal explanation system:
   - Natural language explanations
     * "X causes Y because..."
     * Confidence statements
     * Alternative explanations
   - Causal chain visualization
     * Interactive DAG plots
     * Strength indicators
     * Time delay annotations
   - Anomaly explanation
     * Why did this happen?
     * Root cause analysis
     * Causal attribution
10. Implement causal model validation:
    - Out-of-sample testing
    - Causal cross-validation
    - Stability across periods
    - Robustness checks
    - Expert knowledge integration

Required toolboxes: Econometrics, System Identification, Symbolic Math Toolboxes
Output: Complete CausalInference package with visualization tools
```

## Step 3.4: Meta-Learning System

```
Create a MATLAB meta-learning system for strategy combination and enhancement. The system should:

1. Implement meta-labeling framework:
   - Binary meta-labels (trade/don't trade)
     * Apply to base strategy signals
     * Learn when strategies work
     * Confidence calibration
   - Continuous meta-labels
     * Position size recommendations
     * Holding period optimization
     * Exit timing enhancement
   - Multi-class meta-labels
     * Strategy selection
     * Market regime assignment
     * Risk level classification
2. Create strategy combination algorithms:
   - Linear stacking
     * Ridge/Lasso regression
     * Time-varying weights
     * Cross-validation for weights
   - Non-linear stacking
     * Neural network combiner
     * Random forest blender
     * Gradient boosting meta-model
   - Bayesian model averaging
     * Posterior probability weights
     * Model uncertainty
     * Online updating
3. Build dynamic ensemble weighting:
   - Performance-based weighting
     * Recent Sharpe ratio
     * Rolling accuracy
     * Risk-adjusted returns
   - Diversity-based weighting
     * Strategy correlation
     * Error correlation
     * Output disagreement
   - Adaptive weighting
     * Reinforcement learning weights
     * Regime-specific weights
     * Market condition adaptation
4. Implement strategy correlation analysis:
   - Return correlation matrices
     * Full sample correlation
     * Rolling correlations
     * Conditional correlations
   - Signal correlation
     * Position overlap
     * Timing synchronization
     * Directional agreement
   - Error correlation
     * Drawdown correlation
     * Loss clustering
     * Systematic vs idiosyncratic
5. Create meta-feature engineering:
   - Strategy confidence scores
     * Historical accuracy
     * Recent performance
     * Market condition fit
   - Agreement indicators
     * Multi-strategy consensus
     * Divergence metrics
     * Voting mechanisms
   - Market context features
     * Regime indicators
     * Volatility state
     * Liquidity conditions
6. Build strategy selection framework:
   - Tournament selection
     * Round-robin evaluation
     * Elimination rounds
     * Performance brackets
   - Multi-armed bandit
     * Thompson sampling
     * UCB algorithms
     * Contextual bandits
   - Evolutionary selection
     * Fitness functions
     * Reproduction rules
     * Mutation operators
7. Implement meta-optimization:
   - Hyperparameter optimization
     * Nested cross-validation
     * Bayesian optimization
     * Multi-objective optimization
   - Architecture search
     * Neural architecture search
     * Ensemble structure optimization
     * Feature selection
   - Loss function learning
     * Parameterized loss functions
     * Task-specific losses
     * Adaptive loss shaping
8. Create knowledge transfer system:
   - Transfer between assets
     * Similar asset strategies
     * Cross-market insights
     * Domain adaptation
   - Transfer between timeframes
     * Multi-scale learning
     * Temporal abstraction
     * Resolution adaptation
   - Transfer between regimes
     * Regime-specific knowledge
     * Transition handling
     * Memory mechanisms
9. Build meta-learning evaluation:
   - Meta-validation framework
     * Nested backtesting
     * Meta-overfitting detection
     * Generalization testing
   - Performance attribution
     * Base vs meta contribution
     * Feature importance
     * Decision analysis
   - Stability metrics
     * Weight stability
     * Selection consistency
     * Robustness measures
10. Implement continuous learning:
    - Online meta-learning
      * Incremental updates
      * Concept drift detection
      * Adaptive forgetting
    - Experience replay
      * Important event memory
      * Rare event handling
      * Balanced sampling
    - Curriculum learning
      * Easy to hard progression
      * Staged complexity
      * Adaptive curricula

Required toolboxes: Statistics & ML, Deep Learning, Optimization Toolboxes
Output: Complete MetaLearning package with ensemble optimization tools
```
